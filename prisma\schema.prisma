// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  image         String?
  emailVerified DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // User preferences for personalization
  stylePreferences Json?    // Store style quiz results, preferred colors, etc.
  sizeProfile      Json?    // Store body measurements, preferred fit

  // Relations
  accounts      Account[]
  sessions      Session[]
  designs       Design[]
  orders        Order[]
  customizations Customization[]
  aiTryOnJobs   AiTryOnJob[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// Product Catalog
model Product {
  id          String   @id @default(cuid())
  name        String
  description String   @db.Text
  category    String   // e.g., "t-shirt", "hoodie", "dress"
  subcategory String?  // e.g., "oversized", "fitted", "cropped"

  // Emotional & Lifestyle Appeal
  moodTags    String[] // e.g., ["confident", "playful", "sophisticated"]
  lifestyleTags String[] // e.g., ["streetwear", "athleisure", "workwear"]

  // Imagery for emotional connection
  heroImage     String   // Main product image
  lifestyleImages Json   // Array of lifestyle images with context
  // Example: [{"url": "...", "context": "street", "mood": "confident", "model": "diverse"}]

  // Pricing
  basePrice   Decimal  @db.Decimal(10, 2)
  currency    String   @default("USD")

  // Product status
  isActive    Boolean  @default(true)
  isFeatured  Boolean  @default(false)

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  variants    ProductVariant[]
  templates   Template[]
  orderItems  OrderItem[]
  customizations Customization[]

  @@map("products")
}

// Product Variants (Size, Color combinations)
model ProductVariant {
  id        String  @id @default(cuid())
  sku       String  @unique

  // Relations
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  sizeId    String
  size      Size    @relation(fields: [sizeId], references: [id])
  colorId   String
  color     Color   @relation(fields: [colorId], references: [id])
  fabricId  String?
  fabric    Fabric? @relation(fields: [fabricId], references: [id])

  // Variant-specific data
  price     Decimal? @db.Decimal(10, 2) // Override base price if needed
  stock     Int      @default(0)
  isActive  Boolean  @default(true)

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("product_variants")
}

// Fabrics with emotional appeal
model Fabric {
  id          String   @id @default(cuid())
  name        String   @unique
  description String   @db.Text

  // Emotional attributes
  feelTags    String[] // e.g., ["soft", "luxurious", "breathable", "cozy"]
  careTags    String[] // e.g., ["easy-care", "wrinkle-free", "sustainable"]

  // Technical specs
  composition String   // e.g., "100% Organic Cotton"
  weight      Int?     // GSM (grams per square meter)
  stretch     String?  // e.g., "4-way stretch", "no stretch"

  // Imagery
  textureImage String?
  swatchImage  String?

  // Pricing impact
  priceModifier Decimal @default(0) @db.Decimal(5, 2) // Additional cost

  // Status
  isActive    Boolean  @default(true)

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  variants    ProductVariant[]

  @@map("fabrics")
}

// Colors with emotional associations
model Color {
  id          String   @id @default(cuid())
  name        String   @unique
  hexCode     String   @unique

  // Emotional attributes
  moodTags    String[] // e.g., ["energetic", "calming", "bold", "sophisticated"]
  seasonTags  String[] // e.g., ["spring", "summer", "fall", "winter"]

  // Color family for recommendations
  colorFamily String   // e.g., "warm", "cool", "neutral", "earth"

  // Imagery
  swatchImage String?

  // Status
  isActive    Boolean  @default(true)
  isPopular   Boolean  @default(false)

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  variants    ProductVariant[]

  @@map("colors")
}

// Sizes with fit descriptions
model Size {
  id          String   @id @default(cuid())
  name        String   @unique // e.g., "XS", "S", "M", "L", "XL"
  displayName String   // e.g., "Extra Small", "Small"

  // Size specifications
  measurements Json    // Store chest, waist, hip, length measurements
  fitType     String   // e.g., "regular", "slim", "oversized", "relaxed"

  // Emotional appeal
  fitDescription String @db.Text // e.g., "Perfect for a confident, tailored look"

  // Ordering
  sortOrder   Int      @default(0)

  // Status
  isActive    Boolean  @default(true)

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  variants    ProductVariant[]

  @@map("sizes")
}

// Design Templates with emotional styling
model Template {
  id          String   @id @default(cuid())
  name        String
  description String   @db.Text

  // Emotional & Style Attributes
  moodTag     String   // e.g., "Bold Streetwear", "Minimalist Chic", "Vintage Rebel"
  styleKeywords String[] // e.g., ["urban", "edgy", "confident"]
  targetAudience String // e.g., "young professionals", "creative artists", "fitness enthusiasts"

  // Template data
  designData  Json     // Konva/Fabric.js template data
  previewImage String  // Template preview image

  // Lifestyle context
  lifestyleContext String[] // e.g., ["work", "weekend", "date night", "gym"]

  // Relations
  productId   String
  product     Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Usage tracking
  usageCount  Int      @default(0)

  // Status
  isActive    Boolean  @default(true)
  isFeatured  Boolean  @default(false)

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  customizations Customization[]

  @@map("templates")
}

// User Customizations
model Customization {
  id          String   @id @default(cuid())
  name        String
  description String?

  // Customization data
  designData  Json     // Store the customized design
  previewImage String? // Generated preview

  // Relations
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  productId   String
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  templateId  String?
  template    Template? @relation(fields: [templateId], references: [id])

  // Status
  status      CustomizationStatus @default(DRAFT)

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  orderItems  OrderItem[]
  aiTryOnJobs AiTryOnJob[]

  @@map("customizations")
}

enum CustomizationStatus {
  DRAFT
  COMPLETED
  ORDERED
  ARCHIVED
}

// Pricing Rules for dynamic pricing
model PriceRule {
  id          String   @id @default(cuid())
  name        String
  description String?

  // Rule conditions
  conditions  Json     // Store complex pricing conditions
  // Example: {"minQuantity": 5, "fabricType": "premium", "customizationComplexity": "high"}

  // Price modification
  modifier    Decimal  @db.Decimal(5, 2) // Percentage or fixed amount
  modifierType String  // "percentage" or "fixed"

  // Validity
  validFrom   DateTime?
  validUntil  DateTime?

  // Status
  isActive    Boolean  @default(true)
  priority    Int      @default(0) // Higher priority rules apply first

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("price_rules")
}

// AI Try-On Jobs
model AiTryOnJob {
  id          String   @id @default(cuid())

  // Job details
  status      AiTryOnStatus @default(PENDING)
  jobId       String?  // External AI service job ID

  // Input data
  userPhotoUrl String  // User's photo for try-on
  customizationId String
  customization Customization @relation(fields: [customizationId], references: [id], onDelete: Cascade)

  // Output data
  resultImageUrl String? // Generated try-on image
  confidence    Float?   // AI confidence score
  processingTime Int?    // Processing time in seconds

  // Error handling
  errorMessage  String?
  retryCount    Int      @default(0)

  // Relations
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("ai_try_on_jobs")
}

enum AiTryOnStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

// Design System (Updated)
model Design {
  id          String      @id @default(cuid())
  title       String
  description String?
  imageUrl    String?
  designData  Json        // Store Konva/Fabric.js design data
  category    String
  tags        String[]
  isPublic    Boolean     @default(false)
  status      DesignStatus @default(DRAFT)

  // Metadata
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  userId      String
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("designs")
}

enum DesignStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// Order Management (Updated)
model Order {
  id          String      @id @default(cuid())
  orderNumber String      @unique
  status      OrderStatus @default(PENDING)
  totalAmount Decimal     @db.Decimal(10, 2)
  currency    String      @default("USD")

  // Customer Info
  shippingAddress Json
  billingAddress  Json?

  // Order tracking
  trackingNumber String?
  estimatedDelivery DateTime?

  // Metadata
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  userId      String
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  items       OrderItem[]

  @@map("orders")
}

model OrderItem {
  id       String  @id @default(cuid())
  quantity Int     @default(1)
  price    Decimal @db.Decimal(10, 2)

  // Product details at time of order
  productSnapshot Json // Store product details as they were when ordered

  // Relations
  orderId         String
  order           Order         @relation(fields: [orderId], references: [id], onDelete: Cascade)
  productId       String
  product         Product       @relation(fields: [productId], references: [id])
  customizationId String?
  customization   Customization? @relation(fields: [customizationId], references: [id])

  @@map("order_items")
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}
