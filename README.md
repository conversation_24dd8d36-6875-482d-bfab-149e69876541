# Ottiq — Wear Your Imagination

> **Where AI-powered design meets emotional connection**

Ottiq is not just another custom fashion platform. It's a canvas for self-expression, a bridge between imagination and reality, and a celebration of individual style. We believe that what you wear should tell your story, reflect your dreams, and make you feel unstoppable.

## 🌟 Our Vision

**Emotional Design First**: Every interaction is crafted to spark joy, inspire creativity, and build confidence. We don't just sell clothes — we help you create pieces that make you feel like the best version of yourself.

**AI-Powered Personalization**: Our intelligent design system learns your style preferences, suggests complementary elements, and helps bring your wildest fashion dreams to life.

**Cultural Richness**: Inspired by the vibrant textile traditions of Bangladesh and global fashion heritage, we blend timeless craftsmanship with cutting-edge technology.

## ✨ The Ottiq Experience

### 🎨 **Create Without Limits**
- **Visual Design Editor**: Intuitive drag-and-drop interface powered by Konva.js
- **AI Style Assistant**: Get personalized suggestions based on your preferences
- **Real-time Preview**: See your designs come to life instantly
- **Try-On Technology**: Visualize how your creations will look on you

### 💫 **Express Your Story**
- **Mood-Based Design**: Start with how you want to feel
- **Cultural Elements**: Incorporate traditional patterns and modern aesthetics
- **Personal Symbols**: Add meaningful details that represent you
- **Lifestyle Integration**: Designs that fit your daily adventures

### 🚀 **Premium Quality**
- **Sustainable Materials**: Ethically sourced, environmentally conscious
- **Expert Craftsmanship**: Each piece is carefully constructed
- **Perfect Fit**: Advanced sizing technology for comfort and confidence
- **Fast Delivery**: From design to doorstep in record time

## 🛠 Technical Stack

### Frontend & Design
- **Next.js 15** with App Router for optimal performance
- **TypeScript** for type-safe development
- **Tailwind CSS** with custom Bangladeshi-inspired color palette
- **Framer Motion** for fluid animations and micro-interactions
- **Konva.js** for the visual design editor
- **Zustand** for state management

### Backend & Database
- **PostgreSQL** with Prisma ORM for robust data management
- **NextAuth.js** for secure authentication
- **MinIO** for scalable file storage
- **Redis** for caching and session management

### AI & Integrations
- **Hugging Face** for AI try-on technology
- **Gradio Client** for seamless AI model integration
- **Sharp** for optimized image processing

### Development & Quality
- **Jest** & **Playwright** for comprehensive testing
- **ESLint** & **Prettier** for code quality
- **Husky** for git hooks and automated checks
- **Docker** for consistent development environments

## 🚀 Quick Start

### Prerequisites
- Node.js 20 LTS
- Docker & Docker Compose
- Git

### Development Setup

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd ottiq
   npm install
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   # Update .env with your configuration
   ```

3. **Start Development Services**
   ```bash
   # Start Docker services (PostgreSQL, MinIO, Redis, Mailhog)
   docker-compose up -d
   
   # Setup database
   npm run db:generate
   npm run db:push
   ```

4. **Launch Development Server**
   ```bash
   npm run dev
   ```

5. **Access Your Environment**
   - 🌐 **Application**: http://localhost:3000
   - 🗄️ **Database**: PostgreSQL on localhost:5432
   - 📦 **MinIO Console**: http://localhost:9001
   - 📧 **Mailhog**: http://localhost:8025

## 🧪 Testing

```bash
# Unit tests
npm run test
npm run test:watch
npm run test:coverage

# End-to-end tests
npm run test:e2e
npm run test:e2e:ui

# Code quality
npm run lint
npm run format
npm run type-check
```

## 📁 Project Structure

```
ottiq/
├── src/
│   ├── app/                 # Next.js App Router pages
│   ├── components/          # Reusable UI components
│   ├── lib/                 # Utility libraries and configurations
│   ├── hooks/               # Custom React hooks
│   ├── types/               # TypeScript type definitions
│   └── utils/               # Helper functions
├── prisma/                  # Database schema and migrations
├── scripts/                 # Development and deployment scripts
├── emails/                  # Email templates
├── e2e/                     # End-to-end tests
└── public/                  # Static assets
```

## 🎨 Design Philosophy

### Mobile-First Approach
Every interface is designed for touch, optimized for mobile, and enhanced for desktop.

### Emotional Triggers
- **Desire**: Stunning visuals that make you want to create
- **Self-Expression**: Tools that help you tell your unique story
- **Joy**: Delightful interactions that spark creativity
- **Pride**: The satisfaction of wearing something uniquely yours

### Cultural Authenticity
Drawing inspiration from:
- Traditional Bangladeshi textiles and patterns
- Global fashion heritage
- Contemporary design trends
- Sustainable fashion practices

## 🌍 Contributing

We welcome contributions that align with our vision of emotional, inclusive, and innovative fashion technology.

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.

---

**Ottiq** — Because your imagination deserves to be worn. ✨
